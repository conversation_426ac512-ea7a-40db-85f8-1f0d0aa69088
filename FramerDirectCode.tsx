import React, { useState } from 'react';

// Simplified version for direct Framer use
export default function PodcastNameGenerator() {
  const [input, setInput] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateNames = async () => {
    if (!input.trim()) {
      setError('Please describe what your podcast is about');
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `Create 4 unique, high-converting podcast names for: ${input}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Names should be 2-4 words maximum for memorability
3. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
4. Make names brandable, catchy, and easy to pronounce
5. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}`
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const generatedText = data.candidates[0].content.parts[0].text;
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      setResults(parsedResponse.podcast_names || []);

    } catch (err) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px',
      background: '#ffffff',
      borderRadius: '16px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
    }}>
      <h1 style={{
        fontSize: '2.5rem',
        fontWeight: '800',
        color: '#1a1a1a',
        textAlign: 'center',
        marginBottom: '16px',
        background: 'linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent'
      }}>
        Free Podcast Name Generator
      </h1>
      
      <h2 style={{
        fontSize: '1.2rem',
        color: '#4a5568',
        textAlign: 'center',
        marginBottom: '32px',
        fontWeight: '500'
      }}>
        Create the Perfect Name for Your Podcast in Seconds
      </h2>

      <div style={{ marginBottom: '24px' }}>
        <textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Describe what your podcast is about"
          style={{
            width: '100%',
            padding: '16px',
            fontSize: '1rem',
            border: '2px solid #e1e5e9',
            borderRadius: '12px',
            minHeight: '80px',
            fontFamily: 'inherit',
            resize: 'vertical',
            boxSizing: 'border-box'
          }}
          rows={3}
        />
      </div>

      <button
        onClick={generateNames}
        disabled={loading}
        style={{
          padding: '14px 28px',
          fontSize: '1rem',
          fontWeight: '600',
          color: 'white',
          background: loading ? '#9ca3af' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          borderRadius: '12px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '24px'
        }}
      >
        {loading ? 'Generating...' : 'Generate Names'}
      </button>

      {error && (
        <div style={{
          padding: '16px',
          background: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '12px',
          color: '#dc2626',
          marginBottom: '24px'
        }}>
          {error}
        </div>
      )}

      {results.length > 0 && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px'
        }}>
          {results.map((result, index) => (
            <div key={index} style={{
              background: 'white',
              border: '2px solid #e2e8f0',
              borderRadius: '16px',
              padding: '20px'
            }}>
              <h3 style={{
                margin: '0 0 12px 0',
                color: '#1f2937',
                fontSize: '1.2rem',
                fontWeight: '700'
              }}>
                {result.name}
              </h3>
              <p style={{
                margin: '0',
                color: '#4b5563',
                fontSize: '0.95rem',
                lineHeight: '1.5'
              }}>
                {result.description}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
