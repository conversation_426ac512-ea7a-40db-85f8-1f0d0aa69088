{"name": "podcast-name-generator", "version": "1.0.0", "type": "module", "description": "A React component for generating podcast names using Google Gemini AI", "main": "dist/podcast-name-generator.umd.js", "module": "dist/podcast-name-generator.es.js", "types": "dist/PodcastNameGenerator.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["react", "framer", "podcast", "name-generator", "ai"], "author": "", "license": "ISC", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3"}, "dependencies": {"vite": "^5.4.19"}}