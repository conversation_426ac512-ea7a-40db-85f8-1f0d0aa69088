# Dependencies
node_modules/
/node_modules
**/node_modules

# Build outputs
dist/
/dist
build/
/build
out/
/out

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache directories
.cache/
.parcel-cache/
.vite/
.next/
.nuxt/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Temporary folders
tmp/
temp/
