# Podcast Name Generator - Framer Custom Component

A React component that generates creative podcast names using Google Gemini AI. Perfect for use as a custom component in Framer.com websites.

## 🚀 Features

- **AI-Powered Generation**: Uses Google Gemini Flash 1.5 to create 5 unique, catchy podcast names
- **Smart Descriptions**: Each name comes with an explanation of why it works
- **Copy Functionality**: One-click copying of podcast names with visual feedback
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Loading States**: Beautiful loading animations while generating names
- **Error Handling**: Graceful error handling with user-friendly messages
- **Framer Ready**: Optimized for use as a custom component in Framer

## 📦 Installation & Setup

### For Local Development

1. **Clone or download this project**
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Start development server**:
   ```bash
   npm run dev
   ```
4. **Open browser**: Navigate to `http://localhost:3000`

### For Framer.com Usage

1. **Build the component**:
   ```bash
   npm run build
   ```
2. **Use the built files** in `dist/` folder for Framer import

## 🎯 How to Import into Framer

### Method 1: Direct Component Import (Recommended)

1. **Copy the component files**:
   - Copy `src/PodcastNameGenerator.tsx`
   - Copy `src/PodcastNameGenerator.css`

2. **Create a new component in Framer**:
   - Go to your Framer project
   - Create a new Code Component
   - Paste the content from `PodcastNameGenerator.tsx`
   - Add the CSS styles from `PodcastNameGenerator.css`

3. **Add to your page**:
   - Drag the component onto your canvas
   - Resize as needed (recommended minimum: 800px wide, 600px tall)

### Method 2: Using Built Files

1. **Upload the built files**:
   - Use `dist/podcast-name-generator.es.js`
   - Use `dist/style.css`
   - Import these into your Framer project

## 🔧 Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `apiKey` | string | Provided | Google Gemini API key (optional) |
| `className` | string | '' | Additional CSS classes |
| `style` | object | {} | Inline styles |

## 🎨 Customization

### Styling

The component uses CSS custom properties for easy theming:

```css
.podcast-name-generator {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --text-color: #1a1a1a;
  --background-color: #ffffff;
  --border-color: #e1e5e9;
}
```

### API Configuration

You can use your own Google Gemini API key:

```tsx
<PodcastNameGenerator apiKey="your-api-key-here" />
```

## 🔑 API Setup

### Getting Your Own Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Enable the Gemini API
4. Replace the default API key in the component

### API Usage

The component uses the Gemini 1.5 Flash model with these settings:
- **Temperature**: 0.7 (balanced creativity)
- **Max Tokens**: 1024
- **Top K**: 40
- **Top P**: 0.95

## 📱 Responsive Design

The component is fully responsive and includes:
- **Desktop**: Full-width layout with side-by-side elements
- **Tablet**: Stacked layout with optimized spacing
- **Mobile**: Single-column layout with touch-friendly buttons

## 🎯 Usage Examples

### Basic Usage
```tsx
import PodcastNameGenerator from './PodcastNameGenerator';

function MyPage() {
  return (
    <div>
      <PodcastNameGenerator />
    </div>
  );
}
```

### With Custom Styling
```tsx
<PodcastNameGenerator 
  className="my-custom-class"
  style={{ maxWidth: '600px', margin: '0 auto' }}
/>
```

### With Custom API Key
```tsx
<PodcastNameGenerator 
  apiKey="your-google-gemini-api-key"
/>
```

## 🔍 Testing

The component has been tested with various inputs:
- ✅ Business podcasts
- ✅ Entertainment shows
- ✅ Educational content
- ✅ Interview formats
- ✅ Niche topics

## 🚨 Error Handling

The component handles these error scenarios:
- Invalid API responses
- Network connectivity issues
- Rate limiting
- Empty or invalid inputs
- JSON parsing errors

## 📊 Performance

- **Bundle Size**: ~14KB (ES modules)
- **Load Time**: <100ms initial load
- **API Response**: Typically 2-3 seconds
- **Memory Usage**: Minimal footprint

## 🔒 Security

- API key is configurable (don't hardcode in production)
- Input sanitization included
- CORS-compliant requests
- No sensitive data storage

## 🛠️ Development

### Project Structure
```
src/
├── PodcastNameGenerator.tsx    # Main component
├── PodcastNameGenerator.css    # Component styles
├── FramerComponent.tsx         # Framer-specific wrapper
├── App.tsx                     # Test application
├── App.css                     # Test app styles
└── main.tsx                    # Entry point
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 📄 License

ISC License - Feel free to use in your projects!

## 🤝 Support

If you encounter any issues:
1. Check the browser console for errors
2. Verify your API key is valid
3. Ensure you have internet connectivity
4. Try with a different input description

## 🎉 Ready to Use!

Your Podcast Name Generator component is ready to be imported into Framer! The component will help your website visitors generate creative, AI-powered podcast names that are perfect for their content.

**Recommended Framer Setup:**
- Minimum width: 800px
- Minimum height: 600px
- Allow component to expand vertically as needed
