.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.App-header {
  text-align: center;
  margin-bottom: 40px;
}

.App-header h1 {
  color: #1a1a1a;
  font-size: 2rem;
  margin: 0 0 8px 0;
}

.App-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.App-main {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 60vh;
}

.App-footer {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
  color: #666;
  font-style: italic;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}
